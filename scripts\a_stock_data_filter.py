"""
A股数据智能筛选器
功能：将126GB原始数据筛选压缩到1GB高质量数据
专为A股选股模型优化设计
"""

import pandas as pd
import numpy as np
import os
import logging
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AStockDataFilter:
    def __init__(self):
        self.raw_data_path = r"D:\StockAI_Project\data\raw"
        self.market_data_path = r"D:\StockAI_Project\data\market"
        self.fundamental_data_path = r"D:\StockAI_Project\data\fundamental"
        self.filtered_data_path = r"D:\StockAI_Project\data\filtered"
        
        # 创建目录
        for path in [self.filtered_data_path]:
            os.makedirs(path, exist_ok=True)
        
        # A股优质股票筛选标准
        self.stock_filter_criteria = {
            "min_market_cap": 10_000_000_000,  # 100亿市值
            "min_listing_days": 1095,          # 3年上市时间
            "min_avg_amount": 100_000_000,     # 1亿日均成交额
            "max_suspension_days": 30,         # 最大停牌天数
            "min_price": 5.0,                  # 最低价格
            "max_price": 200.0,                # 最高价格
            "min_roe": 0.08,                   # 最低ROE 8%
            "max_debt_ratio": 0.70,            # 最大资产负债率70%
            "min_data_completeness": 0.95      # 数据完整性95%
        }
        
        # 行业配置（目标300只股票）
        self.industry_allocation = {
            "消费": 30,   # 白酒、食品、医药、零售
            "科技": 80,   # 半导体、软件、新能源、通信
            "金融": 40,   # 银行、保险、券商
            "周期": 60,   # 化工、钢铁、有色、建材
            "公用": 30,   # 电力、交运、地产
            "其他": 60    # 机械、军工、农业等
        }
    
    def load_stock_basic_info(self):
        """加载股票基础信息"""
        logger.info("加载股票基础信息...")
        
        # 这里假设有一个股票基础信息文件
        basic_info_file = os.path.join(self.raw_data_path, "stock_basic_info.csv")
        
        if os.path.exists(basic_info_file):
            df = pd.read_csv(basic_info_file)
            logger.info(f"加载股票基础信息: {len(df)} 只股票")
            return df
        else:
            logger.warning("未找到股票基础信息文件，将基于现有数据推断")
            return self.infer_stock_basic_info()
    
    def infer_stock_basic_info(self):
        """从现有数据推断股票基础信息"""
        logger.info("从现有数据推断股票基础信息...")
        
        stock_info = []
        
        # 扫描市场数据目录
        if os.path.exists(self.market_data_path):
            for file in os.listdir(self.market_data_path):
                if file.endswith('.csv'):
                    stock_code = os.path.splitext(file)[0]
                    
                    try:
                        # 读取股票数据样本
                        df = pd.read_csv(os.path.join(self.market_data_path, file), nrows=1000)
                        
                        if len(df) > 0:
                            # 推断基础信息
                            avg_price = df['close'].mean() if 'close' in df.columns else df.get('成交价', pd.Series([0])).mean()
                            avg_volume = df['volume'].mean() if 'volume' in df.columns else df.get('手数', pd.Series([0])).mean()
                            
                            stock_info.append({
                                'stock_code': stock_code,
                                'avg_price': avg_price,
                                'avg_volume': avg_volume,
                                'data_points': len(df),
                                'industry': self.infer_industry(stock_code)
                            })
                    
                    except Exception as e:
                        logger.warning(f"处理 {stock_code} 时出错: {str(e)}")
        
        return pd.DataFrame(stock_info)
    
    def infer_industry(self, stock_code):
        """根据股票代码推断行业"""
        # 简化的行业推断逻辑
        if stock_code.startswith('00'):
            return "科技" if int(stock_code[2:4]) < 50 else "消费"
        elif stock_code.startswith('30'):
            return "科技"
        elif stock_code.startswith('60'):
            if int(stock_code[2:4]) < 20:
                return "金融"
            elif int(stock_code[2:4]) < 50:
                return "周期"
            else:
                return "消费"
        else:
            return "其他"
    
    def filter_quality_stocks(self, stock_info_df):
        """筛选优质股票"""
        logger.info("开始筛选优质股票...")
        
        # 基础筛选
        filtered_stocks = stock_info_df[
            (stock_info_df['avg_price'] >= self.stock_filter_criteria['min_price']) &
            (stock_info_df['avg_price'] <= self.stock_filter_criteria['max_price']) &
            (stock_info_df['data_points'] >= 1000)  # 确保有足够数据
        ].copy()
        
        logger.info(f"基础筛选后剩余: {len(filtered_stocks)} 只股票")
        
        # 按行业分配筛选
        selected_stocks = []
        
        for industry, target_count in self.industry_allocation.items():
            industry_stocks = filtered_stocks[filtered_stocks['industry'] == industry]
            
            if len(industry_stocks) > 0:
                # 按平均成交量排序，选择流动性好的股票
                industry_stocks = industry_stocks.sort_values('avg_volume', ascending=False)
                selected_count = min(target_count, len(industry_stocks))
                selected_stocks.extend(industry_stocks.head(selected_count)['stock_code'].tolist())
                
                logger.info(f"{industry}行业: 目标{target_count}只，实际选择{selected_count}只")
        
        logger.info(f"最终选择股票数量: {len(selected_stocks)}")
        return selected_stocks
    
    def process_tick_to_5min(self, tick_file_path, stock_code):
        """将分时数据聚合为5分钟K线"""
        logger.info(f"处理 {stock_code} 的分时数据...")
        
        try:
            # 读取分时数据
            df = pd.read_csv(tick_file_path)
            
            # 标准化列名
            column_mapping = {
                '时间': 'time', '成交价': 'price', '手数': 'volume', '买卖方向': 'direction'
            }
            df = df.rename(columns=column_mapping)
            
            # 转换时间格式
            df['time'] = pd.to_datetime(df['time'])
            
            # 筛选交易时间
            df = df[
                ((df['time'].dt.hour == 9) & (df['time'].dt.minute >= 30)) |
                ((df['time'].dt.hour >= 10) & (df['time'].dt.hour <= 11)) |
                ((df['time'].dt.hour >= 13) & (df['time'].dt.hour <= 14)) |
                ((df['time'].dt.hour == 15) & (df['time'].dt.minute == 0))
            ]
            
            # 创建5分钟时间分组
            df['time_5min'] = df['time'].dt.floor('5min')
            
            # 聚合为5分钟K线
            kline_5min = df.groupby('time_5min').agg({
                'price': ['first', 'max', 'min', 'last'],
                'volume': 'sum'
            }).round(2)
            
            # 重命名列
            kline_5min.columns = ['open', 'high', 'low', 'close', 'volume']
            kline_5min = kline_5min.reset_index()
            kline_5min.rename(columns={'time_5min': 'datetime'}, inplace=True)
            
            # 计算成交额
            kline_5min['amount'] = kline_5min['close'] * kline_5min['volume']
            
            # 筛选时间范围（最近24个月）
            cutoff_date = datetime.now() - timedelta(days=730)
            kline_5min = kline_5min[kline_5min['datetime'] >= cutoff_date]
            
            logger.info(f"{stock_code}: 生成5分钟K线 {len(kline_5min)} 条")
            return kline_5min
            
        except Exception as e:
            logger.error(f"处理 {stock_code} 分时数据时出错: {str(e)}")
            return pd.DataFrame()
    
    def calculate_data_quality_score(self, df):
        """计算数据质量评分"""
        if len(df) == 0:
            return 0.0
        
        # 数据完整性
        completeness = 1 - df.isnull().sum().sum() / (len(df) * len(df.columns))
        
        # 价格连续性（避免大幅跳空）
        if 'close' in df.columns:
            price_changes = df['close'].pct_change().abs()
            continuity = 1 - (price_changes > 0.1).sum() / len(price_changes)
        else:
            continuity = 1.0
        
        # 成交量稳定性
        if 'volume' in df.columns and len(df) > 10:
            volume_cv = df['volume'].std() / df['volume'].mean()
            volume_stability = max(0, 1 - volume_cv / 2)
        else:
            volume_stability = 1.0
        
        # 综合评分
        quality_score = (completeness * 0.5 + continuity * 0.3 + volume_stability * 0.2)
        return quality_score
    
    def filter_and_save_stock_data(self, selected_stocks):
        """筛选并保存股票数据"""
        logger.info("开始筛选和保存股票数据...")
        
        processed_count = 0
        total_size = 0
        
        for stock_code in selected_stocks:
            try:
                # 处理分时数据
                tick_file = os.path.join(self.market_data_path, f"{stock_code}.csv")
                
                if os.path.exists(tick_file):
                    # 转换为5分钟K线
                    kline_5min = self.process_tick_to_5min(tick_file, stock_code)
                    
                    if not kline_5min.empty:
                        # 计算数据质量评分
                        quality_score = self.calculate_data_quality_score(kline_5min)
                        
                        if quality_score >= self.stock_filter_criteria['min_data_completeness']:
                            # 保存筛选后的数据
                            output_file = os.path.join(self.filtered_data_path, f"{stock_code}_5min.csv")
                            kline_5min.to_csv(output_file, index=False)
                            
                            file_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
                            total_size += file_size
                            processed_count += 1
                            
                            logger.info(f"✓ {stock_code}: 质量评分{quality_score:.3f}, 文件大小{file_size:.1f}MB")
                        else:
                            logger.warning(f"✗ {stock_code}: 数据质量不达标 {quality_score:.3f}")
                    else:
                        logger.warning(f"✗ {stock_code}: 数据处理失败")
                else:
                    logger.warning(f"✗ {stock_code}: 原始文件不存在")
                    
            except Exception as e:
                logger.error(f"处理 {stock_code} 时出错: {str(e)}")
        
        logger.info(f"数据筛选完成!")
        logger.info(f"成功处理: {processed_count}/{len(selected_stocks)} 只股票")
        logger.info(f"总文件大小: {total_size:.1f} MB ({total_size/1024:.2f} GB)")
        
        return processed_count, total_size
    
    def run_filtering(self):
        """运行完整的数据筛选流程"""
        logger.info("=" * 60)
        logger.info("A股数据智能筛选器启动")
        logger.info("=" * 60)
        
        # 1. 加载股票基础信息
        stock_info = self.load_stock_basic_info()
        
        if stock_info.empty:
            logger.error("无法获取股票基础信息")
            return False
        
        # 2. 筛选优质股票
        selected_stocks = self.filter_quality_stocks(stock_info)
        
        if not selected_stocks:
            logger.error("未筛选出符合条件的股票")
            return False
        
        # 3. 处理和保存数据
        processed_count, total_size = self.filter_and_save_stock_data(selected_stocks)
        
        # 4. 生成筛选报告
        self.generate_filter_report(selected_stocks, processed_count, total_size)
        
        return True
    
    def generate_filter_report(self, selected_stocks, processed_count, total_size):
        """生成筛选报告"""
        report = {
            "筛选时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "目标股票数": len(selected_stocks),
            "成功处理数": processed_count,
            "成功率": f"{processed_count/len(selected_stocks)*100:.1f}%",
            "数据大小": f"{total_size:.1f} MB ({total_size/1024:.2f} GB)",
            "压缩比例": "预计从126GB压缩到约1GB",
            "数据质量": "仅保留数据完整性>95%的优质股票",
            "时间范围": "最近24个月的5分钟K线数据"
        }
        
        # 保存报告
        report_file = os.path.join(self.filtered_data_path, "filter_report.txt")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("A股数据筛选报告\n")
            f.write("=" * 40 + "\n")
            for key, value in report.items():
                f.write(f"{key}: {value}\n")
        
        logger.info("筛选报告已保存到: " + report_file)
        
        # 打印报告
        print("\n" + "=" * 60)
        print("A股数据筛选完成报告")
        print("=" * 60)
        for key, value in report.items():
            print(f"{key}: {value}")
        print("=" * 60)

def main():
    """主函数"""
    filter_engine = AStockDataFilter()
    success = filter_engine.run_filtering()
    
    if success:
        print("\n🎉 数据筛选成功完成！")
        print("筛选后的数据已保存到 data/filtered/ 目录")
        print("可以继续进行特征工程和模型训练")
    else:
        print("\n❌ 数据筛选失败，请检查日志信息")

if __name__ == "__main__":
    main()
