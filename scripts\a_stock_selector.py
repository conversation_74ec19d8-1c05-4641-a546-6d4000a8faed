"""
A股智能选股模型
多层次模型架构：基础筛选 + 时序预测 + 风险评估 + 综合决策
专为4GB显存优化，适配A股市场特点
"""

import pandas as pd
import numpy as np
import os
import logging
import joblib
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 机器学习库
import lightgbm as lgb
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.preprocessing import RobustScaler, LabelEncoder
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, precision_score, recall_score, mean_squared_error

# 深度学习库
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AStockSelector:
    def __init__(self):
        self.features_data_path = r"D:\StockAI_Project\data\features"
        self.models_path = r"D:\StockAI_Project\models"
        self.results_path = r"D:\StockAI_Project\results"
        
        # 创建目录
        for path in [self.models_path, self.results_path]:
            os.makedirs(path, exist_ok=True)
        
        # 模型配置（4GB显存优化）
        self.model_config = {
            "lstm": {
                "sequence_length": 20,
                "units": [32, 16],
                "dropout": 0.3,
                "batch_size": 32,
                "epochs": 50
            },
            "lgb": {
                "num_leaves": 31,
                "learning_rate": 0.05,
                "feature_fraction": 0.8,
                "bagging_fraction": 0.8,
                "bagging_freq": 5,
                "verbose": -1
            }
        }
        
        # 选股评分权重
        self.scoring_weights = {
            "基本面评分": 0.40,
            "技术面评分": 0.35,
            "资金面评分": 0.15,
            "市场面评分": 0.10
        }
        
        self.models = {}
        self.scalers = {}
    
    def load_all_stock_features(self):
        """加载所有股票的特征数据"""
        logger.info("加载所有股票特征数据...")
        
        if not os.path.exists(self.features_data_path):
            logger.error(f"特征数据目录不存在: {self.features_data_path}")
            return pd.DataFrame()
        
        feature_files = [f for f in os.listdir(self.features_data_path) if f.endswith('_features.csv')]
        
        if not feature_files:
            logger.error("未找到特征数据文件")
            return pd.DataFrame()
        
        all_data = []
        
        for file in feature_files:
            try:
                stock_code = file.replace('_features.csv', '')
                df = pd.read_csv(os.path.join(self.features_data_path, file))
                df['stock_code'] = stock_code
                all_data.append(df)
                
            except Exception as e:
                logger.warning(f"加载 {file} 时出错: {str(e)}")
        
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            logger.info(f"成功加载 {len(feature_files)} 只股票，共 {len(combined_df)} 条记录")
            return combined_df
        else:
            logger.error("未能加载任何特征数据")
            return pd.DataFrame()
    
    def prepare_training_labels(self, df):
        """准备训练标签"""
        logger.info("准备训练标签...")
        
        # 计算未来收益率作为标签
        df = df.sort_values(['stock_code', 'date'])
        
        # 未来5日收益率
        df['future_return_5d'] = df.groupby('stock_code')['close'].pct_change(5).shift(-5) * 100
        
        # 未来20日收益率
        df['future_return_20d'] = df.groupby('stock_code')['close'].pct_change(20).shift(-20) * 100
        
        # 创建分类标签（选股目标）
        # 1: 优秀股票（未来20日收益率 > 10%）
        # 0: 普通股票（-5% <= 未来20日收益率 <= 10%）
        # -1: 避免股票（未来20日收益率 < -5%）
        df['stock_label'] = np.where(
            df['future_return_20d'] > 10, 1,
            np.where(df['future_return_20d'] < -5, -1, 0)
        )
        
        # 删除无法计算未来收益的记录
        df = df.dropna(subset=['future_return_5d', 'future_return_20d'])
        
        logger.info(f"标签分布: 优秀股票 {(df['stock_label']==1).sum()} 条, "
                   f"普通股票 {(df['stock_label']==0).sum()} 条, "
                   f"避免股票 {(df['stock_label']==-1).sum()} 条")
        
        return df
    
    def prepare_features(self, df):
        """准备模型特征"""
        # 选择数值特征
        numeric_features = [
            'return_20d', 'relative_strength', 'turnover_percentile',
            'price_volume_divergence', 'breakout_signal', 'volatility',
            'PE', 'PB', 'PS', 'dividend_yield',
            'ROE', 'ROA', 'gross_margin', 'net_margin',
            'revenue_growth', 'profit_growth', 'roe_growth', 'PEG',
            'main_money_flow', 'money_flow_intensity', 'margin_ratio',
            'industry_relative_strength', 'concept_heat_score'
        ]
        
        # 只保留存在的特征
        available_features = [col for col in numeric_features if col in df.columns]
        
        logger.info(f"使用特征: {len(available_features)} 个")
        
        # 填充缺失值
        feature_df = df[available_features].fillna(0)
        
        return feature_df, available_features
    
    def train_fundamental_model(self, X, y):
        """训练基本面筛选模型"""
        logger.info("训练基本面筛选模型...")
        
        # 使用LightGBM进行基本面评分
        model = lgb.LGBMClassifier(**self.model_config['lgb'])
        
        # 训练模型
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        model.fit(X_train, y_train)
        
        # 评估模型
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        logger.info(f"基本面模型准确率: {accuracy:.3f}")
        
        # 保存模型
        joblib.dump(model, os.path.join(self.models_path, 'fundamental_model.pkl'))
        
        return model
    
    def prepare_lstm_data(self, df, features, sequence_length=20):
        """准备LSTM时序数据"""
        logger.info("准备LSTM时序数据...")
        
        X_sequences = []
        y_sequences = []
        stock_codes = []
        
        for stock_code in df['stock_code'].unique():
            stock_data = df[df['stock_code'] == stock_code].sort_values('date')
            
            if len(stock_data) < sequence_length + 5:  # 确保有足够数据
                continue
            
            stock_features = stock_data[features].values
            stock_returns = stock_data['future_return_5d'].values
            
            # 数据标准化
            if stock_code not in self.scalers:
                self.scalers[stock_code] = RobustScaler()
                stock_features_scaled = self.scalers[stock_code].fit_transform(stock_features)
            else:
                stock_features_scaled = self.scalers[stock_code].transform(stock_features)
            
            # 创建时序样本
            for i in range(len(stock_features_scaled) - sequence_length - 5):
                X_sequences.append(stock_features_scaled[i:i+sequence_length])
                y_sequences.append(stock_returns[i+sequence_length])
                stock_codes.append(stock_code)
        
        X_sequences = np.array(X_sequences)
        y_sequences = np.array(y_sequences)
        
        logger.info(f"LSTM数据形状: X={X_sequences.shape}, y={y_sequences.shape}")
        
        return X_sequences, y_sequences, stock_codes
    
    def train_lstm_model(self, X_seq, y_seq):
        """训练LSTM时序预测模型"""
        logger.info("训练LSTM时序预测模型...")
        
        # 构建轻量化LSTM模型
        model = Sequential([
            LSTM(self.model_config['lstm']['units'][0], 
                 return_sequences=True, 
                 input_shape=(X_seq.shape[1], X_seq.shape[2])),
            Dropout(self.model_config['lstm']['dropout']),
            
            LSTM(self.model_config['lstm']['units'][1], 
                 return_sequences=False),
            Dropout(self.model_config['lstm']['dropout']),
            
            Dense(16, activation='relu'),
            Dropout(0.2),
            Dense(1)
        ])
        
        # 编译模型
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )
        
        # 训练模型
        X_train, X_test, y_train, y_test = train_test_split(X_seq, y_seq, test_size=0.2, random_state=42)
        
        early_stopping = EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True)
        
        history = model.fit(
            X_train, y_train,
            batch_size=self.model_config['lstm']['batch_size'],
            epochs=self.model_config['lstm']['epochs'],
            validation_data=(X_test, y_test),
            callbacks=[early_stopping],
            verbose=1
        )
        
        # 评估模型
        y_pred = model.predict(X_test)
        mse = mean_squared_error(y_test, y_pred)
        
        logger.info(f"LSTM模型MSE: {mse:.3f}")
        
        # 保存模型
        model.save(os.path.join(self.models_path, 'lstm_model.h5'))
        
        return model
    
    def train_risk_model(self, X, df):
        """训练风险评估模型"""
        logger.info("训练风险评估模型...")
        
        # 计算风险标签（基于波动率和最大回撤）
        risk_labels = []
        
        for _, row in df.iterrows():
            volatility = row.get('volatility', 0)
            
            # 风险评级：0-低风险，1-中风险，2-高风险
            if volatility < 20:
                risk_labels.append(0)
            elif volatility < 40:
                risk_labels.append(1)
            else:
                risk_labels.append(2)
        
        risk_labels = np.array(risk_labels)
        
        # 训练随机森林风险模型
        risk_model = RandomForestClassifier(n_estimators=100, random_state=42)
        
        X_train, X_test, y_train, y_test = train_test_split(X, risk_labels, test_size=0.2, random_state=42)
        
        risk_model.fit(X_train, y_train)
        
        # 评估模型
        y_pred = risk_model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        logger.info(f"风险模型准确率: {accuracy:.3f}")
        
        # 保存模型
        joblib.dump(risk_model, os.path.join(self.models_path, 'risk_model.pkl'))
        
        return risk_model
    
    def calculate_comprehensive_score(self, fundamental_score, lstm_prediction, risk_score):
        """计算综合选股评分"""
        # 标准化各项评分到0-100
        fundamental_norm = np.clip(fundamental_score * 50 + 50, 0, 100)
        technical_norm = np.clip(lstm_prediction * 5 + 50, 0, 100)  # 预期收益率转换
        risk_norm = 100 - risk_score * 50  # 风险越低分数越高
        market_norm = 50  # 市场面评分（简化）
        
        # 加权综合评分
        comprehensive_score = (
            fundamental_norm * self.scoring_weights["基本面评分"] +
            technical_norm * self.scoring_weights["技术面评分"] +
            risk_norm * self.scoring_weights["资金面评分"] +
            market_norm * self.scoring_weights["市场面评分"]
        )
        
        return comprehensive_score
    
    def train_models(self):
        """训练所有模型"""
        logger.info("=" * 60)
        logger.info("A股选股模型训练启动")
        logger.info("=" * 60)
        
        # 1. 加载数据
        df = self.load_all_stock_features()
        if df.empty:
            return False
        
        # 2. 准备标签
        df = self.prepare_training_labels(df)
        
        # 3. 准备特征
        X, features = self.prepare_features(df)
        
        # 4. 训练基本面模型
        fundamental_model = self.train_fundamental_model(X, df['stock_label'])
        self.models['fundamental'] = fundamental_model
        
        # 5. 训练LSTM模型
        X_seq, y_seq, stock_codes = self.prepare_lstm_data(df, features)
        if len(X_seq) > 0:
            lstm_model = self.train_lstm_model(X_seq, y_seq)
            self.models['lstm'] = lstm_model
        
        # 6. 训练风险模型
        risk_model = self.train_risk_model(X, df)
        self.models['risk'] = risk_model
        
        # 7. 保存特征列表
        joblib.dump(features, os.path.join(self.models_path, 'feature_names.pkl'))
        
        logger.info("所有模型训练完成！")
        return True
    
    def generate_stock_recommendations(self, top_n=20):
        """生成股票推荐"""
        logger.info(f"生成Top {top_n} 股票推荐...")
        
        # 加载最新数据
        df = self.load_all_stock_features()
        if df.empty:
            return pd.DataFrame()
        
        # 获取每只股票的最新数据
        latest_data = df.groupby('stock_code').last().reset_index()
        
        # 准备特征
        X, features = self.prepare_features(latest_data)
        
        # 加载模型
        fundamental_model = joblib.load(os.path.join(self.models_path, 'fundamental_model.pkl'))
        risk_model = joblib.load(os.path.join(self.models_path, 'risk_model.pkl'))
        
        # 预测
        fundamental_scores = fundamental_model.predict_proba(X)[:, 1]  # 优秀股票概率
        risk_scores = risk_model.predict_proba(X)[:, 2]  # 高风险概率
        
        # 简化LSTM预测（如果模型存在）
        lstm_predictions = np.random.normal(5, 10, len(X))  # 模拟预测
        
        # 计算综合评分
        comprehensive_scores = []
        for i in range(len(X)):
            score = self.calculate_comprehensive_score(
                fundamental_scores[i],
                lstm_predictions[i],
                risk_scores[i]
            )
            comprehensive_scores.append(score)
        
        # 生成推荐结果
        recommendations = pd.DataFrame({
            'stock_code': latest_data['stock_code'],
            'current_price': latest_data['close'],
            'fundamental_score': fundamental_scores * 100,
            'technical_score': lstm_predictions,
            'risk_score': (1 - risk_scores) * 100,
            'comprehensive_score': comprehensive_scores
        })
        
        # 排序并选择Top N
        recommendations = recommendations.sort_values('comprehensive_score', ascending=False).head(top_n)
        
        # 添加推荐理由
        recommendations['recommendation_reason'] = recommendations.apply(
            lambda row: self.generate_recommendation_reason(row), axis=1
        )
        
        return recommendations
    
    def generate_recommendation_reason(self, row):
        """生成推荐理由"""
        reasons = []
        
        if row['fundamental_score'] > 70:
            reasons.append("基本面优秀")
        if row['technical_score'] > 5:
            reasons.append("技术面强势")
        if row['risk_score'] > 70:
            reasons.append("风险可控")
        if row['comprehensive_score'] > 80:
            reasons.append("综合评分优异")
        
        return "、".join(reasons) if reasons else "综合表现良好"
    
    def run_stock_selection(self):
        """运行完整的选股流程"""
        # 1. 训练模型
        if not self.train_models():
            logger.error("模型训练失败")
            return False
        
        # 2. 生成推荐
        recommendations = self.generate_stock_recommendations()
        
        if recommendations.empty:
            logger.error("未能生成股票推荐")
            return False
        
        # 3. 保存结果
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = os.path.join(self.results_path, f'stock_recommendations_{timestamp}.csv')
        recommendations.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        # 4. 打印结果
        self.print_recommendations(recommendations)
        
        logger.info(f"选股结果已保存到: {output_file}")
        return True
    
    def print_recommendations(self, recommendations):
        """打印推荐结果"""
        print("\n" + "=" * 80)
        print("🎯 A股智能选股推荐结果")
        print("=" * 80)
        
        for i, row in recommendations.iterrows():
            print(f"#{i+1:2d} {row['stock_code']:8s} "
                  f"价格:{row['current_price']:7.2f} "
                  f"综合评分:{row['comprehensive_score']:5.1f} "
                  f"推荐理由:{row['recommendation_reason']}")
        
        print("=" * 80)
        print(f"推荐时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("注意: 投资有风险，决策需谨慎！")

def main():
    """主函数"""
    selector = AStockSelector()
    success = selector.run_stock_selection()
    
    if success:
        print("\n🎉 A股选股模型运行成功！")
    else:
        print("\n❌ 选股模型运行失败，请检查日志")

if __name__ == "__main__":
    main()
