@echo off
chcp 65001 >nul
echo ================================================================
echo                    A股智能选股系统
echo                   一键运行完整流程
echo ================================================================
echo.

:: 检查虚拟环境
if not exist "venv\Scripts\activate.bat" (
    echo ❌ 虚拟环境不存在，请先运行 create_venv.bat
    pause
    exit /b 1
)

:: 激活虚拟环境
echo 🔧 激活虚拟环境...
call venv\Scripts\activate.bat

:: 检查必要目录
echo 📁 检查目录结构...
if not exist "data" mkdir data
if not exist "data\raw" mkdir data\raw
if not exist "data\market" mkdir data\market
if not exist "data\fundamental" mkdir data\fundamental
if not exist "data\filtered" mkdir data\filtered
if not exist "data\features" mkdir data\features
if not exist "models" mkdir models
if not exist "results" mkdir results

echo ✅ 目录结构检查完成
echo.

:: 步骤1：数据筛选
echo ================================================================
echo 步骤1/3：数据智能筛选 (126GB → 1GB)
echo ================================================================
echo 🔍 开始筛选A股优质股票数据...
python scripts/a_stock_data_filter.py

if %errorlevel% neq 0 (
    echo ❌ 数据筛选失败
    pause
    exit /b 1
)

echo ✅ 数据筛选完成
echo.

:: 步骤2：特征工程
echo ================================================================
echo 步骤2/3：A股特色特征工程 (25个核心因子)
echo ================================================================
echo 🧠 开始计算选股因子...
python scripts/a_stock_features.py

if %errorlevel% neq 0 (
    echo ❌ 特征工程失败
    pause
    exit /b 1
)

echo ✅ 特征工程完成
echo.

:: 步骤3：选股模型
echo ================================================================
echo 步骤3/3：智能选股模型 (多层次架构)
echo ================================================================
echo 🎯 开始训练选股模型并生成推荐...
python scripts/a_stock_selector.py

if %errorlevel% neq 0 (
    echo ❌ 选股模型运行失败
    pause
    exit /b 1
)

echo.
echo ================================================================
echo                    🎉 A股选股系统运行完成！
echo ================================================================
echo.
echo 📊 处理结果：
echo    • 数据筛选：data/filtered/
echo    • 特征数据：data/features/
echo    • 训练模型：models/
echo    • 选股结果：results/
echo.
echo 💡 系统特色：
echo    • 126GB数据智能压缩到1GB
echo    • 25个A股特色选股因子
echo    • 多层次模型架构
echo    • 4GB显存优化设计
echo    • 综合评分选股推荐
echo.
echo 📈 下一步建议：
echo    1. 查看 results/ 目录中的选股推荐
echo    2. 根据推荐结果进行投资决策
echo    3. 定期重新运行更新推荐
echo.
echo ⚠️  投资提醒：股市有风险，投资需谨慎！
echo    本系统仅供参考，不构成投资建议。
echo.

:: 打开结果目录
if exist "results" (
    echo 🔍 打开结果目录...
    start explorer results
)

echo 按任意键退出...
pause >nul
