# A股智能选股系统配置文件
# A-Stock Intelligent Selection System Configuration

# 数据配置
data:
  raw_data_path: "data/raw"
  market_data_path: "data/market"
  fundamental_data_path: "data/fundamental"
  filtered_data_path: "data/filtered"
  features_data_path: "data/features"
  
  # A股数据筛选参数
  filtering:
    target_stocks: 300                    # 目标股票数量
    min_market_cap: 10000000000          # 最小市值(100亿)
    min_listing_days: 1095               # 最小上市天数(3年)
    min_avg_amount: 100000000            # 最小日均成交额(1亿)
    max_suspension_days: 30              # 最大停牌天数
    min_price: 5.0                       # 最低价格
    max_price: 200.0                     # 最高价格
    min_data_completeness: 0.95          # 最小数据完整性
    
    # 行业配置
    industry_allocation:
      消费: 30    # 白酒、食品、医药、零售
      科技: 80    # 半导体、软件、新能源、通信
      金融: 40    # 银行、保险、券商
      周期: 60    # 化工、钢铁、有色、建材
      公用: 30    # 电力、交运、地产
      其他: 60    # 机械、军工、农业等
  
  # 时间配置
  time_config:
    tick_to_5min: true                   # 分时转5分钟K线
    trading_hours:
      morning: ["09:30", "11:30"]
      afternoon: ["13:00", "15:00"]
    history_months: 24                   # 历史数据月数
    fundamental_years: 6                 # 财务数据年数

# A股特色特征工程
features:
  # 25个核心选股因子
  core_factors:
    # 价值因子 (5个)
    value_factors: ["PE", "PB", "PS", "EV_EBITDA", "dividend_yield"]
    
    # 质量因子 (5个)
    quality_factors: ["ROE", "ROA", "gross_margin", "net_margin", "ROIC"]
    
    # 成长因子 (4个)
    growth_factors: ["revenue_growth", "profit_growth", "roe_growth", "PEG"]
    
    # 技术因子 (6个)
    technical_factors: ["return_20d", "relative_strength", "turnover_percentile", 
                       "price_volume_divergence", "breakout_signal", "volatility"]
    
    # 资金因子 (3个)
    money_factors: ["main_money_flow", "money_flow_intensity", "margin_ratio"]
    
    # 市场因子 (2个)
    market_factors: ["industry_relative_strength", "concept_heat_score"]
  
  # 技术指标参数
  technical_params:
    ma_periods: [5, 10, 20, 50]
    rsi_period: 14
    bb_period: 20
    bb_std: 2
    volatility_window: 20

# 多层次模型架构
models:
  # 第一层：基础筛选模型
  fundamental_model:
    type: "LightGBM"
    params:
      num_leaves: 31
      learning_rate: 0.05
      feature_fraction: 0.8
      bagging_fraction: 0.8
      bagging_freq: 5
      verbose: -1
    target: "investment_value_probability"
  
  # 第二层：时序预测模型
  lstm_model:
    type: "LSTM"
    params:
      sequence_length: 20
      units: [32, 16]
      dropout: 0.3
      batch_size: 32
      epochs: 50
      learning_rate: 0.001
    target: "future_return_prediction"
  
  # 第三层：风险评估模型
  risk_model:
    type: "RandomForest"
    params:
      n_estimators: 100
      max_depth: 10
      min_samples_split: 5
      random_state: 42
    target: "risk_level"

# 选股评分体系
scoring:
  weights:
    fundamental_score: 0.40    # 基本面评分权重
    technical_score: 0.35      # 技术面评分权重
    money_flow_score: 0.15     # 资金面评分权重
    market_score: 0.10         # 市场面评分权重
  
  # 评分标准
  criteria:
    excellent_threshold: 80     # 优秀股票阈值
    good_threshold: 60         # 良好股票阈值
    risk_threshold: 30         # 风险股票阈值

# 系统配置
system:
  # GPU优化配置 (4GB显存)
  gpu:
    memory_limit: 4096         # GPU内存限制(MB)
    mixed_precision: true      # 混合精度训练
    allow_growth: true         # 允许动态增长
  
  # 内存优化
  memory:
    batch_processing: true     # 分批处理
    cache_size: 500           # 缓存大小(MB)
    data_compression: true     # 数据压缩
  
  # 日志配置
  logging:
    level: "INFO"
    format: "%(asctime)s - %(levelname)s - %(message)s"
    file: "logs/a_stock_selection.log"
  
  # 输出配置
  output:
    top_recommendations: 20    # 推荐股票数量
    save_models: true         # 保存模型
    generate_reports: true    # 生成报告
    export_format: ["csv", "excel"]

# A股市场特殊处理
market_specific:
  # 涨跌停处理
  limit_handling:
    up_limit_threshold: 0.095   # 涨停阈值
    down_limit_threshold: -0.095 # 跌停阈值
    exclude_limit_days: true    # 排除涨跌停日
  
  # ST股票处理
  st_handling:
    exclude_st: true           # 排除ST股票
    exclude_delisting: true    # 排除退市风险股
  
  # 新股处理
  ipo_handling:
    min_listing_days: 365      # 新股最小上市天数
    separate_modeling: true    # 新股单独建模
  
  # 停牌复牌处理
  suspension_handling:
    max_suspension_days: 30    # 最大停牌天数
    resume_adjustment_days: 3  # 复牌后调整天数

# 回测验证配置
backtesting:
  # 回测参数
  params:
    start_date: "2022-01-01"   # 回测开始日期
    end_date: "2024-12-31"     # 回测结束日期
    initial_capital: 1000000   # 初始资金(100万)
    max_positions: 20          # 最大持仓数
    rebalance_frequency: "monthly"  # 调仓频率
  
  # 风险控制
  risk_control:
    max_single_weight: 0.1     # 单只股票最大权重10%
    stop_loss: -0.15           # 止损线-15%
    take_profit: 0.30          # 止盈线+30%
    max_drawdown: -0.20        # 最大回撤-20%
  
  # 评价指标
  metrics:
    - "total_return"           # 总收益率
    - "annual_return"          # 年化收益率
    - "sharpe_ratio"           # 夏普比率
    - "max_drawdown"           # 最大回撤
    - "win_rate"               # 胜率
    - "profit_loss_ratio"      # 盈亏比

# 实盘交易配置 (仅供参考)
live_trading:
  # 交易参数
  params:
    auto_trading: false        # 自动交易(默认关闭)
    position_sizing: "equal_weight"  # 等权重配置
    rebalance_time: "09:35"    # 调仓时间
    max_order_amount: 100000   # 单笔最大交易金额
  
  # 风控参数
  risk_management:
    daily_loss_limit: -0.05    # 日损失限制-5%
    position_limit: 0.95       # 仓位限制95%
    blacklist_check: true      # 黑名单检查
    
  # 通知配置
  notifications:
    email_alerts: false        # 邮件提醒
    wechat_alerts: false       # 微信提醒
    trade_confirmations: true  # 交易确认
