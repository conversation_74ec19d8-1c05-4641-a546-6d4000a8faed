"""
A股特色特征工程模块
构建25个核心选股因子，专为A股市场优化
"""

import pandas as pd
import numpy as np
import talib
import os
import logging
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AStockFeatureEngine:
    def __init__(self):
        self.filtered_data_path = r"D:\StockAI_Project\data\filtered"
        self.fundamental_data_path = r"D:\StockAI_Project\data\fundamental"
        self.features_data_path = r"D:\StockAI_Project\data\features"
        
        # 创建输出目录
        os.makedirs(self.features_data_path, exist_ok=True)
        
        # A股核心选股因子定义
        self.core_factors = {
            "价值因子": ["PE", "PB", "PS", "EV_EBITDA", "股息率"],
            "质量因子": ["ROE", "ROA", "毛利率", "净利率", "ROIC"],
            "成长因子": ["营收增长率", "净利润增长率", "ROE增长率", "PEG"],
            "技术因子": ["20日涨跌幅", "相对强度", "换手率分位", "量价背离", "突破信号", "波动率"],
            "资金因子": ["主力资金净流入", "北向资金偏好", "融资融券比例"],
            "市场因子": ["行业相对强度", "概念热度评分"]
        }
    
    def load_stock_data(self, stock_code):
        """加载股票的K线数据"""
        file_path = os.path.join(self.filtered_data_path, f"{stock_code}_5min.csv")
        
        if os.path.exists(file_path):
            df = pd.read_csv(file_path)
            df['datetime'] = pd.to_datetime(df['datetime'])
            df = df.sort_values('datetime')
            return df
        else:
            logger.warning(f"未找到 {stock_code} 的数据文件")
            return pd.DataFrame()
    
    def load_fundamental_data(self, stock_code):
        """加载财务数据"""
        file_path = os.path.join(self.fundamental_data_path, f"{stock_code}_fundamental.csv")
        
        if os.path.exists(file_path):
            df = pd.read_csv(file_path)
            df['date'] = pd.to_datetime(df['date'])
            return df
        else:
            logger.warning(f"未找到 {stock_code} 的财务数据")
            return pd.DataFrame()
    
    def aggregate_to_daily(self, df_5min):
        """将5分钟数据聚合为日线数据"""
        if df_5min.empty:
            return pd.DataFrame()
        
        df_5min['date'] = df_5min['datetime'].dt.date
        
        daily_data = df_5min.groupby('date').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum',
            'amount': 'sum'
        }).reset_index()
        
        daily_data['date'] = pd.to_datetime(daily_data['date'])
        return daily_data.sort_values('date')
    
    def calculate_technical_factors(self, daily_df):
        """计算技术因子"""
        if len(daily_df) < 60:  # 需要足够的历史数据
            logger.warning("数据量不足，无法计算技术指标")
            return daily_df
        
        # 1. 20日涨跌幅
        daily_df['return_20d'] = daily_df['close'].pct_change(20) * 100
        
        # 2. 相对强度 (相对于市场的表现)
        daily_df['ma_20'] = daily_df['close'].rolling(20).mean()
        daily_df['relative_strength'] = (daily_df['close'] / daily_df['ma_20'] - 1) * 100
        
        # 3. 换手率分位数
        daily_df['turnover_rate'] = daily_df['volume'] / daily_df['volume'].rolling(60).mean()
        daily_df['turnover_percentile'] = daily_df['turnover_rate'].rolling(60).rank(pct=True) * 100
        
        # 4. 量价背离指标
        price_change = daily_df['close'].pct_change()
        volume_change = daily_df['volume'].pct_change()
        daily_df['price_volume_divergence'] = (price_change * volume_change).rolling(5).mean()
        
        # 5. 突破信号
        daily_df['bb_upper'] = daily_df['close'].rolling(20).mean() + daily_df['close'].rolling(20).std() * 2
        daily_df['bb_lower'] = daily_df['close'].rolling(20).mean() - daily_df['close'].rolling(20).std() * 2
        daily_df['breakout_signal'] = np.where(
            daily_df['close'] > daily_df['bb_upper'], 1,
            np.where(daily_df['close'] < daily_df['bb_lower'], -1, 0)
        )
        
        # 6. 波动率
        daily_df['volatility'] = daily_df['close'].pct_change().rolling(20).std() * np.sqrt(252) * 100
        
        return daily_df
    
    def calculate_value_factors(self, daily_df, fundamental_df):
        """计算价值因子"""
        if fundamental_df.empty:
            logger.warning("无财务数据，跳过价值因子计算")
            return daily_df
        
        # 合并财务数据（前向填充）
        merged_df = pd.merge_asof(
            daily_df.sort_values('date'),
            fundamental_df.sort_values('date'),
            on='date',
            direction='backward'
        )
        
        # 计算市值
        if 'total_shares' in merged_df.columns:
            merged_df['market_cap'] = merged_df['close'] * merged_df['total_shares']
        
        # 1. PE比率
        if 'net_profit' in merged_df.columns and 'market_cap' in merged_df.columns:
            merged_df['PE'] = merged_df['market_cap'] / (merged_df['net_profit'] * 4)  # 年化
        
        # 2. PB比率
        if 'net_assets' in merged_df.columns and 'market_cap' in merged_df.columns:
            merged_df['PB'] = merged_df['market_cap'] / merged_df['net_assets']
        
        # 3. PS比率
        if 'revenue' in merged_df.columns and 'market_cap' in merged_df.columns:
            merged_df['PS'] = merged_df['market_cap'] / (merged_df['revenue'] * 4)  # 年化
        
        # 4. 股息率 (简化计算)
        if 'dividend' in merged_df.columns:
            merged_df['dividend_yield'] = merged_df['dividend'] / merged_df['close'] * 100
        
        return merged_df
    
    def calculate_quality_factors(self, df):
        """计算质量因子"""
        # 1. ROE
        if 'net_profit' in df.columns and 'net_assets' in df.columns:
            df['ROE'] = df['net_profit'] / df['net_assets'] * 100
        
        # 2. ROA
        if 'net_profit' in df.columns and 'total_assets' in df.columns:
            df['ROA'] = df['net_profit'] / df['total_assets'] * 100
        
        # 3. 毛利率
        if 'gross_profit' in df.columns and 'revenue' in df.columns:
            df['gross_margin'] = df['gross_profit'] / df['revenue'] * 100
        
        # 4. 净利率
        if 'net_profit' in df.columns and 'revenue' in df.columns:
            df['net_margin'] = df['net_profit'] / df['revenue'] * 100
        
        return df
    
    def calculate_growth_factors(self, df):
        """计算成长因子"""
        # 1. 营收增长率
        if 'revenue' in df.columns:
            df['revenue_growth'] = df['revenue'].pct_change(4) * 100  # 同比增长
        
        # 2. 净利润增长率
        if 'net_profit' in df.columns:
            df['profit_growth'] = df['net_profit'].pct_change(4) * 100  # 同比增长
        
        # 3. ROE增长率
        if 'ROE' in df.columns:
            df['roe_growth'] = df['ROE'].pct_change(4) * 100
        
        # 4. PEG比率
        if 'PE' in df.columns and 'profit_growth' in df.columns:
            df['PEG'] = df['PE'] / df['profit_growth']
        
        return df
    
    def calculate_money_flow_factors(self, daily_df):
        """计算资金流因子"""
        # 1. 主力资金净流入 (简化计算)
        # 大单定义：单笔成交额 > 日均成交额的1%
        avg_amount = daily_df['amount'].rolling(20).mean()
        daily_df['big_order_threshold'] = avg_amount * 0.01
        
        # 主力资金净流入比例
        daily_df['main_money_flow'] = (daily_df['amount'] - avg_amount) / avg_amount * 100
        
        # 2. 资金流入强度
        daily_df['money_flow_intensity'] = daily_df['main_money_flow'].rolling(5).mean()
        
        # 3. 融资融券比例 (模拟数据)
        daily_df['margin_ratio'] = np.random.normal(0.1, 0.05, len(daily_df))  # 模拟数据
        
        return daily_df
    
    def calculate_market_factors(self, daily_df, stock_code):
        """计算市场因子"""
        # 1. 行业相对强度 (简化计算)
        industry = self.get_stock_industry(stock_code)
        daily_df['industry_relative_strength'] = np.random.normal(0, 10, len(daily_df))  # 模拟
        
        # 2. 概念热度评分 (基于成交量活跃度)
        volume_ma = daily_df['volume'].rolling(20).mean()
        daily_df['concept_heat_score'] = (daily_df['volume'] / volume_ma - 1) * 100
        
        return daily_df
    
    def get_stock_industry(self, stock_code):
        """获取股票行业"""
        # 简化的行业分类
        if stock_code.startswith('00'):
            return "科技" if int(stock_code[2:4]) < 50 else "消费"
        elif stock_code.startswith('30'):
            return "科技"
        elif stock_code.startswith('60'):
            if int(stock_code[2:4]) < 20:
                return "金融"
            elif int(stock_code[2:4]) < 50:
                return "周期"
            else:
                return "消费"
        else:
            return "其他"
    
    def select_core_features(self, df):
        """选择25个核心特征"""
        core_features = [
            'date', 'close', 'volume', 'amount',  # 基础数据
            
            # 技术因子 (6个)
            'return_20d', 'relative_strength', 'turnover_percentile',
            'price_volume_divergence', 'breakout_signal', 'volatility',
            
            # 价值因子 (4个) - 如果有财务数据
            'PE', 'PB', 'PS', 'dividend_yield',
            
            # 质量因子 (4个)
            'ROE', 'ROA', 'gross_margin', 'net_margin',
            
            # 成长因子 (4个)
            'revenue_growth', 'profit_growth', 'roe_growth', 'PEG',
            
            # 资金因子 (3个)
            'main_money_flow', 'money_flow_intensity', 'margin_ratio',
            
            # 市场因子 (2个)
            'industry_relative_strength', 'concept_heat_score'
        ]
        
        # 只保留存在的列
        available_features = [col for col in core_features if col in df.columns]
        
        logger.info(f"选择特征: {len(available_features)}/{len(core_features)} 个")
        
        return df[available_features]
    
    def process_stock_features(self, stock_code):
        """处理单只股票的特征工程"""
        logger.info(f"处理 {stock_code} 的特征工程...")
        
        try:
            # 1. 加载数据
            df_5min = self.load_stock_data(stock_code)
            if df_5min.empty:
                return False
            
            # 2. 聚合为日线数据
            daily_df = self.aggregate_to_daily(df_5min)
            if daily_df.empty:
                return False
            
            # 3. 计算技术因子
            daily_df = self.calculate_technical_factors(daily_df)
            
            # 4. 加载财务数据并计算基本面因子
            fundamental_df = self.load_fundamental_data(stock_code)
            if not fundamental_df.empty:
                daily_df = self.calculate_value_factors(daily_df, fundamental_df)
                daily_df = self.calculate_quality_factors(daily_df)
                daily_df = self.calculate_growth_factors(daily_df)
            
            # 5. 计算资金流因子
            daily_df = self.calculate_money_flow_factors(daily_df)
            
            # 6. 计算市场因子
            daily_df = self.calculate_market_factors(daily_df, stock_code)
            
            # 7. 选择核心特征
            features_df = self.select_core_features(daily_df)
            
            # 8. 保存特征数据
            output_file = os.path.join(self.features_data_path, f"{stock_code}_features.csv")
            features_df.to_csv(output_file, index=False)
            
            logger.info(f"✓ {stock_code}: 特征工程完成，{len(features_df)} 条记录，{len(features_df.columns)} 个特征")
            return True
            
        except Exception as e:
            logger.error(f"处理 {stock_code} 特征工程时出错: {str(e)}")
            return False
    
    def run_feature_engineering(self):
        """运行批量特征工程"""
        logger.info("=" * 60)
        logger.info("A股特征工程启动")
        logger.info("=" * 60)
        
        # 获取所有筛选后的股票文件
        if not os.path.exists(self.filtered_data_path):
            logger.error(f"筛选数据目录不存在: {self.filtered_data_path}")
            return False
        
        stock_files = [f for f in os.listdir(self.filtered_data_path) if f.endswith('_5min.csv')]
        
        if not stock_files:
            logger.error("未找到筛选后的股票数据")
            return False
        
        logger.info(f"找到 {len(stock_files)} 只股票待处理")
        
        # 批量处理
        success_count = 0
        for stock_file in stock_files:
            stock_code = stock_file.replace('_5min.csv', '')
            
            if self.process_stock_features(stock_code):
                success_count += 1
        
        logger.info(f"特征工程完成: {success_count}/{len(stock_files)} 只股票处理成功")
        
        # 生成特征报告
        self.generate_feature_report(success_count, len(stock_files))
        
        return True
    
    def generate_feature_report(self, success_count, total_count):
        """生成特征工程报告"""
        report = {
            "处理时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "处理股票数": f"{success_count}/{total_count}",
            "成功率": f"{success_count/total_count*100:.1f}%",
            "核心特征数": "25个A股选股因子",
            "特征类别": "技术6个+价值4个+质量4个+成长4个+资金3个+市场2个+基础2个",
            "输出目录": self.features_data_path
        }
        
        print("\n" + "=" * 60)
        print("A股特征工程完成报告")
        print("=" * 60)
        for key, value in report.items():
            print(f"{key}: {value}")
        print("=" * 60)

def main():
    """主函数"""
    feature_engine = AStockFeatureEngine()
    success = feature_engine.run_feature_engineering()
    
    if success:
        print("\n🎉 A股特征工程成功完成！")
        print("特征数据已保存到 data/features/ 目录")
        print("可以继续进行选股模型训练")
    else:
        print("\n❌ 特征工程失败，请检查日志信息")

if __name__ == "__main__":
    main()
